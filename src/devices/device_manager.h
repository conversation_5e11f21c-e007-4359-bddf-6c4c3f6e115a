/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_DEVICE_MANAGER_H
#define AUBO_COFFEE_SERVICE_DEVICE_MANAGER_H

#include <memory>
#include <map>
#include <string>


#include "robotic_arms/coffee_arm.h"
#include "robotic_arms/ingredient_arm.h"
#include "cup_dispenser/cup_dispenser.h"
#include "coffee_machine/coffee_machine.h"
#include "milk_container_cleaner/milk_container_cleaner.h"

namespace aubo {

/**
 * @class DeviceManager
 * @brief 设备管理器类
 * 
 * 统一管理咖啡制作系统中的所有设备
 */
class DeviceManager {
public:
    /**
     * @brief 构造函数
     */
    DeviceManager();

    /**
     * @brief 析构函数
     */
    ~DeviceManager();

    /**
     * @brief 初始化所有设备
     * @return 初始化成功返回true
     */
    bool initialize_all_devices();

    /**
     * @brief 关闭所有设备
     * @return 关闭成功返回true
     */
    bool shutdown_all_devices();

    /**
     * @brief 紧急停止所有设备
     * @return 紧急停止成功返回true
     */
    bool emergency_stop_all_devices();

    /**
     * @brief 获取咖啡机械
     * @return 咖啡机械指针
     */
    std::shared_ptr<CoffeeArm> get_coffee_arm() const;

    /**
     * @brief 获取配料机械
     * @return 配料机械指针
     */
    std::shared_ptr<IngredientArm> get_ingredient_arm() const;

    /**
     * @brief 获取杯子分配器
     * @return 杯子分配器指针
     */
    std::shared_ptr<CupDispenser> get_cup_dispenser() const;

    /**
     * @brief 获取咖啡机
     * @return 咖啡机指针
     */
    std::shared_ptr<CoffeeMachine> get_coffee_machine() const;

    /**
     * @brief 获取牛奶容器清洁器
     * @return 牛奶容器清洁器指针
     */
    std::shared_ptr<MilkContainerCleaner> get_milk_container_cleaner() const;



private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_DEVICE_MANAGER_H
