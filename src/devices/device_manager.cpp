/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "device_manager.h"

#include <sstream>
#include <vector>

#include <aubo-base/log.h>

namespace aubo {

class DeviceManager::Impl {
public:
    Impl() {
        // 创建所有设备实例
        main_robotic_arm_ = std::make_shared<MainRoboticArm>();
        sub_robotic_arm_ = std::make_shared<SubRoboticArm>();
        cup_dispenser_ = std::make_shared<CupDispenser>();
        coffee_machine_ = std::make_shared<CoffeeMachine>();
        milk_container_cleaner_ = std::make_shared<MilkContainerCleaner>();
    }

    bool initialize_all_devices() {
        LOG_INFO("[DeviceManager] 开始初始化所有设备");

        bool all_success = true;
        int success_count = 0;
        int total_count = 5;

        // 初始化主机械臂
        if (main_robotic_arm_->init()) {
            LOG_INFO("[DeviceManager] 主机械臂初始化成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 主机械臂初始化失败");
            all_success = false;
        }

        // 初始化副机械臂
        if (sub_robotic_arm_->init()) {
            LOG_INFO("[DeviceManager] 副机械臂初始化成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 副机械臂初始化失败");
            all_success = false;
        }

        // 初始化杯子分配器
        if (cup_dispenser_->init()) {
            LOG_INFO("[DeviceManager] 杯子分配器初始化成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 杯子分配器初始化失败");
            all_success = false;
        }

        // 初始化咖啡机
        if (coffee_machine_->init()) {
            LOG_INFO("[DeviceManager] 咖啡机初始化成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 咖啡机初始化失败");
            all_success = false;
        }

        // 初始化牛奶容器清洁器
        if (milk_container_cleaner_->init()) {
            LOG_INFO("[DeviceManager] 牛奶容器清洁器初始化成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 牛奶容器清洁器初始化失败");
            all_success = false;
        }

        LOG_INFO("[DeviceManager] 设备初始化完成: {}/{} 成功", success_count, total_count);
        return all_success;
    }

    bool shutdown_all_devices() {
        LOG_INFO("[DeviceManager] 开始关闭所有设备");

        bool all_success = true;
        int success_count = 0;
        int total_count = 5;

        // 逆序关闭设备
        if (milk_container_cleaner_->shutdown()) {
            LOG_INFO("[DeviceManager] 牛奶容器清洁器关闭成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 牛奶容器清洁器关闭失败");
            all_success = false;
        }

        if (coffee_machine_->shutdown()) {
            LOG_INFO("[DeviceManager] 咖啡机关闭成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 咖啡机关闭失败");
            all_success = false;
        }

        if (cup_dispenser_->shutdown()) {
            LOG_INFO("[DeviceManager] 杯子分配器关闭成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 杯子分配器关闭失败");
            all_success = false;
        }

        if (sub_robotic_arm_->shutdown()) {
            LOG_INFO("[DeviceManager] 副机械臂关闭成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 副机械臂关闭失败");
            all_success = false;
        }

        if (main_robotic_arm_->shutdown()) {
            LOG_INFO("[DeviceManager] 主机械臂关闭成功");
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 主机械臂关闭失败");
            all_success = false;
        }

        LOG_INFO("[DeviceManager] 设备关闭完成: {}/{} 成功", success_count, total_count);
        return all_success;
    }

    bool emergency_stop_all_devices() {
        LOG_WARN("[DeviceManager] 紧急停止所有设备");

        bool all_success = true;
        int success_count = 0;
        int total_count = 5;

        // 紧急停止所有设备
        if (main_robotic_arm_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 主机械臂紧急停止失败");
            all_success = false;
        }

        if (sub_robotic_arm_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 副机械臂紧急停止失败");
            all_success = false;
        }

        if (cup_dispenser_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 杯子分配器紧急停止失败");
            all_success = false;
        }

        if (coffee_machine_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 咖啡机紧急停止失败");
            all_success = false;
        }

        if (milk_container_cleaner_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("[DeviceManager] 牛奶容器清洁器紧急停止失败");
            all_success = false;
        }

        LOG_WARN("[DeviceManager] 紧急停止完成: {}/{} 成功", success_count, total_count);
        return all_success;
    }

    std::shared_ptr<MainRoboticArm> get_main_robotic_arm() const {
        return main_robotic_arm_;
    }

    std::shared_ptr<SubRoboticArm> get_sub_robotic_arm() const {
        return sub_robotic_arm_;
    }

    std::shared_ptr<CupDispenser> get_cup_dispenser() const {
        return cup_dispenser_;
    }

    std::shared_ptr<CoffeeMachine> get_coffee_machine() const {
        return coffee_machine_;
    }

    std::shared_ptr<MilkContainerCleaner> get_milk_container_cleaner() const {
        return milk_container_cleaner_;
    }



private:
    std::shared_ptr<MainRoboticArm> main_robotic_arm_;
    std::shared_ptr<SubRoboticArm> sub_robotic_arm_;
    std::shared_ptr<CupDispenser> cup_dispenser_;
    std::shared_ptr<CoffeeMachine> coffee_machine_;
    std::shared_ptr<MilkContainerCleaner> milk_container_cleaner_;
    

};

// DeviceManager 公共接口实现
DeviceManager::DeviceManager() {
    impl_ = std::make_unique<Impl>();
}

DeviceManager::~DeviceManager() = default;

bool DeviceManager::initialize_all_devices() {
    return impl_->initialize_all_devices();
}

bool DeviceManager::shutdown_all_devices() {
    return impl_->shutdown_all_devices();
}

bool DeviceManager::emergency_stop_all_devices() {
    return impl_->emergency_stop_all_devices();
}

std::shared_ptr<MainRoboticArm> DeviceManager::get_main_robotic_arm() const {
    return impl_->get_main_robotic_arm();
}

std::shared_ptr<SubRoboticArm> DeviceManager::get_sub_robotic_arm() const {
    return impl_->get_sub_robotic_arm();
}

std::shared_ptr<CupDispenser> DeviceManager::get_cup_dispenser() const {
    return impl_->get_cup_dispenser();
}

std::shared_ptr<CoffeeMachine> DeviceManager::get_coffee_machine() const {
    return impl_->get_coffee_machine();
}

std::shared_ptr<MilkContainerCleaner> DeviceManager::get_milk_container_cleaner() const {
    return impl_->get_milk_container_cleaner();
}



} // namespace aubo
