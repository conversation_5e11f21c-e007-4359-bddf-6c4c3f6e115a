/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "ingredient_arm.h"

#include <chrono>
#include <cmath>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "../../core/coffee_order.h"
#include "latte_art_config.h"
#include "robot_base.h"

namespace aubo {

// 配置常量
constexpr const char* INGREDIENT_ARM_HOST = "*************";
constexpr int INGREDIENT_ARM_PORT = 8899;

class IngredientArm::Impl : public RobotBase {
public:
    Impl(IngredientArm* parent) : RobotBase("IngredientArm", INGREDIENT_ARM_HOST, INGREDIENT_ARM_PORT), parent_(parent) {}

    ~Impl() {
        LOG_INFO("[IngredientArm] ~Impl");
    }

    bool on_init_specific() override {
        LOG_INFO("[IngredientArm] 执行配料臂特定初始化");

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[IngredientArm] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[IngredientArm] 拉花配置加载成功");
        }

        LOG_INFO("[IngredientArm] 配料臂特定初始化完成");
        return true;
    }

    bool execute_action(const std::string& action_name, nlohmann::json action_params) override {
        LOG_INFO("[IngredientArm] 执行动作: {}", action_name);
        // TODO: 实现通用动作执行逻辑
        return true;
    }

private:
    LatteArtConfig latte_art_config_;
};

IngredientArm::IngredientArm() {
    impl_ = std::make_unique<Impl>(this);
}

IngredientArm::~IngredientArm() = default;

bool IngredientArm::init() {
    return impl_->init();
}

bool IngredientArm::shutdown() {
    return impl_->shutdown();
}

bool IngredientArm::emergency_stop() {
    return impl_->emergency_stop();
}

bool IngredientArm::execute_action(const std::string& action_name, nlohmann::json action_params) {
    return impl_->execute_action(action_name, action_params);
}

} // namespace aubo
