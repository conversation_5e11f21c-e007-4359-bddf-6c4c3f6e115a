/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_ARM_H
#define AUBO_COFFEE_SERVICE_COFFEE_ARM_H

#include <memory>
#include "robot_base.h"


namespace aubo {

/**
 * @class CoffeeArm
 * @brief 控制咖啡服务的咖啡机器臂
 *
 * 该类用于控制咖啡服务中的咖啡机器臂。它主要负责拿杯子、接咖啡、交付咖啡等操作。
 */
class CoffeeArm {
public:
    /**
     * @brief 构造函数
     */
    CoffeeArm();

    /**
     * @brief 析构函数
     */
    ~CoffeeArm();

    /**
     * @brief 初始化机器人
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机器人连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    bool execute_action(const std::string& action_name, nlohmann::json action_params = nlohmann::json());

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_ARM_H
