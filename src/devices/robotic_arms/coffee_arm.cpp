/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_arm.h"

#include <chrono>
#include <cmath>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "../../core/coffee_order.h"
#include "latte_art_config.h"
#include "robot_base.h"

namespace aubo {

// 配置常量
constexpr const char* COFFEE_ARM_HOST = "*************";
constexpr int COFFEE_ARM_PORT = 8899;

class CoffeeArm::Impl : public RobotBase {
public:
    Impl(CoffeeArm* parent) : RobotBase("CoffeeArm", COFFEE_ARM_HOST, COFFEE_ARM_PORT), parent_(parent) {}

    ~Impl() {
        LOG_INFO("[CoffeeArm] ~Impl");
    }

    bool on_init_specific() override {
        LOG_INFO("[CoffeeArm] 执行咖啡臂特定初始化");

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[CoffeeArm] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[CoffeeArm] 拉花配置加载成功");
        }

        LOG_INFO("[CoffeeArm] 咖啡臂特定初始化完成");
        return true;
    }

    bool execute_action(const std::string& action_name, nlohmann::json action_params) override {
        LOG_INFO("[CoffeeArm] 执行动作: {}", action_name);
        // TODO: 实现通用动作执行逻辑
        return true;
    }

private:
    LatteArtConfig latte_art_config_;
};


CoffeeArm::CoffeeArm() {
    impl_ = std::make_unique<Impl>(this);
}

CoffeeArm::~CoffeeArm() = default;

bool CoffeeArm::init() {
    return impl_->init();
}

bool CoffeeArm::shutdown() {
    return impl_->shutdown();
}

bool CoffeeArm::emergency_stop() {
    return impl_->emergency_stop();
}

bool CoffeeArm::execute_action(const std::string& action_name, nlohmann::json action_params) {
    return impl_->execute_action(action_name, action_params);
}

} // namespace aubo
