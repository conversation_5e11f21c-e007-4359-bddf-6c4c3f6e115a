/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_INGREDIENT_ROBOTIC_ARM_H
#define AUBO_COFFEE_SERVICE_INGREDIENT_ROBOTIC_ARM_H

#include <memory>

#include "robotic_arm_base.h"


namespace aubo {

/**
 * @class IngredientRoboticArm
 * @brief 控制咖啡服务的配料机械臂
 *
 * 该类用于控制咖啡服务中的配料机械臂。它主要负责接牛奶、接冰块等配料操作。
 * 配料机械臂与咖啡机械臂协调工作，在咖啡制作过程中提供必要的配料添加操作。
 */
class IngredientRoboticArm {
public:
    /**
     * @brief 构造函数
     */
    IngredientRoboticArm();

    /**
     * @brief 析构函数
     */
    ~IngredientRoboticArm();

    /**
     * @brief 初始化机械臂
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机械臂连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    bool execute_action(const std::string& action_name, nlohmann::json action_params = nlohmann::json());

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_INGREDIENT_ROBOTIC_ARM_H
