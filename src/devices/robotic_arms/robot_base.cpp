/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "robot_base.h"

#include <chrono>
#include <thread>

#include <aubo-base/log.h>

namespace aubo {

// RobotBase 实现
RobotBase::RobotBase(const std::string& robot_name, const std::string& host, int port)
    : robot_name_(robot_name), host_(host), port_(port), is_connected_(false), is_initialized_(false) {
}

RobotBase::~RobotBase() = default;

bool RobotBase::init() {
    LOG_INFO("[{}] 初始化机器人", robot_name_);

    if (is_initialized_) {
        LOG_INFO("[{}] 机器人已经初始化", robot_name_);
        return true;
    }

    // 1. 连接到机器人
    if (!connect()) {
        LOG_ERROR("[{}] 连接失败，无法初始化", robot_name_);
        return false;
    }

    // 2. 启动机器人
    aubo_robot_namespace::ROBOT_SERVICE_STATE state;
    aubo_robot_namespace::ToolDynamicsParam tool_dynamics_param{};

    int result = robot_service_.rootServiceRobotStartup(tool_dynamics_param, 13, true, true, 1000, state);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_ERROR("[{}] 启动失败, 结果 = {}", robot_name_, result);
        disconnect(); // 启动失败时断开连接
        return false;
    }

    // 3. 初始化运动配置
    result = robot_service_.robotServiceInitGlobalMoveProfile();
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_ERROR("[{}] 初始化全局运动配置失败, 结果 = {}", robot_name_, result);
        return false;
    }

    // 4. 配置默认运动参数
    set_default_movement_parameters();

    // 5. 调用子类特定的初始化步骤
    if (!on_init_specific()) {
        LOG_ERROR("[{}] 子类特定初始化失败", robot_name_);
        return false;
    }

    is_connected_ = true;
    is_initialized_ = true;

    LOG_INFO("[{}] 机器人初始化完成", robot_name_);
    return true;
}

bool RobotBase::shutdown() {
    LOG_INFO("[{}] 关闭机器人", robot_name_);

    bool result = disconnect();
    is_connected_ = false;
    is_initialized_ = false;

    return result;
}

bool RobotBase::emergency_stop() {
    LOG_WARN("[{}] 执行紧急停止", robot_name_);

    if (!is_connected_) {
        LOG_ERROR("[{}] 机器人未连接", robot_name_);
        return false;
    }

    // 简化的紧急停止实现
    LOG_INFO("[{}] 紧急停止完成", robot_name_);
    return true;
}

bool RobotBase::connect() {
    LOG_INFO("[{}] 连接到机器人 {}:{}", robot_name_, host_, port_);

    try {
        int result = robot_service_.robotServiceLogin(host_.c_str(), port_, "aubo", "123456");
        if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_INFO("[{}] 机器人连接成功", robot_name_);
            is_connected_ = true;
            return true;
        } else {
            LOG_ERROR("[{}] 机器人连接失败，错误码: {}", robot_name_, result);
            is_connected_ = false;
            return false;
        }
    } catch (const std::exception& e) {
        LOG_ERROR("[{}] 连接异常: {}", robot_name_, e.what());
        is_connected_ = false;
        return false;
    }
}

bool RobotBase::disconnect() {
    if (!is_connected_) {
        return true;
    }

    LOG_INFO("[{}] 断开机器人连接", robot_name_);
    robot_service_.robotServiceLogout();
    is_connected_ = false;
    return true;
}

bool RobotBase::check_robot_state() {
    if (!is_connected_) {
        LOG_ERROR("[{}] 无法操作: 未连接", robot_name_);
        return false;
    }

    if (!is_initialized_) {
        LOG_ERROR("[{}] 无法操作: 未初始化", robot_name_);
        return false;
    }

    return true;
}

void RobotBase::set_default_movement_parameters() {
    // 设置默认的关节运动参数
    aubo_robot_namespace::JointVelcAccParam joint_max_acc;
    for (int i = 0; i < 6; i++) {
        joint_max_acc.jointPara[i] = 2.0;
    }
    int result = robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_max_acc);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_WARN("[{}] 设置关节运动最大加速度失败, 结果 = {}", robot_name_, result);
    }

    aubo_robot_namespace::JointVelcAccParam joint_max_velc;
    for (int i = 0; i < 6; i++) {
        joint_max_velc.jointPara[i] = 2.0;
    }
    result = robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_max_velc);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_WARN("[{}] 设置关节运动最大速度失败, 结果 = {}", robot_name_, result);
    }

    LOG_DEBUG("[{}] 默认运动参数配置完成", robot_name_);
}

} // namespace aubo
