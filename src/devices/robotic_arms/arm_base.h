/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_ARM_BASE_H
#define AUBO_COFFEE_SERVICE_ARM_BASE_H

#include <string>
#include <memory>

#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>
#include <nlohmann/json.hpp>

namespace aubo {

/**
 * @class ArmBase
 * @brief 机械基类
 *
 * 提供所有机械的通用功能实现
 */
class ArmBase {
public:
    /**
     * @brief 构造函数
     * @param arm_name 机械名称
     * @param host 机械IP地址
     * @param port 机械端口
     */
    ArmBase(const std::string& arm_name, const std::string& host, int port);

    /**
     * @brief 虚析构函数
     */
    virtual ~ArmBase();

    /**
     * @brief 初始化机械（包含连接、启动、配置等完整流程）
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机械连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    virtual bool execute_action(const std::string& action_name, nlohmann::json action_params) = 0;

    /**
     * @brief 获取机械名称
     * @return 机械名称
     */
    const std::string& get_name() const { return arm_name_; }

protected:
    /**
     * @brief 连接到机械
     * @return 连接成功返回true
     */
    bool connect();

    /**
     * @brief 断开机械连接
     * @return 断开成功返回true
     */
    bool disconnect();

    /**
     * @brief 检查机械状态
     * @return 状态正常返回true
     */
    bool check_robot_state();

    /**
     * @brief 子类特定的初始化步骤（子类可重写）
     * @return 初始化成功返回true
     */
    virtual bool on_init_specific() { return true; }

    /**
     * @brief 设置默认的运动参数
     */
    void set_default_movement_parameters();

protected:
    std::string arm_name_;          ///< 机械名称
    std::string host_;              ///< 机械IP地址
    int port_;                      ///< 机械端口
    bool is_connected_;             ///< 连接状态
    bool is_initialized_;           ///< 初始化状态

    // 机械服务接口
    ServiceInterface robot_service_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_ARM_BASE_H
