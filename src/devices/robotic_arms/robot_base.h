/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_ROBOT_BASE_H
#define AUBO_COFFEE_SERVICE_ROBOT_BASE_H

#include <string>
#include <memory>

#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>
#include <nlohmann/json.hpp>

namespace aubo {

/**
 * @class RobotBase
 * @brief 机器人基类
 *
 * 提供所有机器人的通用功能实现
 */
class RobotBase {
public:
    /**
     * @brief 构造函数
     * @param robot_name 机器人名称
     * @param host 机器人IP地址
     * @param port 机器人端口
     */
    RobotBase(const std::string& robot_name, const std::string& host, int port);

    /**
     * @brief 虚析构函数
     */
    virtual ~RobotBase();

    /**
     * @brief 初始化机器人（包含连接、启动、配置等完整流程）
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机器人连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    virtual bool execute_action(const std::string& action_name, nlohmann::json action_params) = 0;

    /**
     * @brief 获取机器人名称
     * @return 机器人名称
     */
    const std::string& get_name() const { return robot_name_; }

protected:
    /**
     * @brief 连接到机器人
     * @return 连接成功返回true
     */
    bool connect();

    /**
     * @brief 断开机器人连接
     * @return 断开成功返回true
     */
    bool disconnect();

    /**
     * @brief 检查机器人状态
     * @return 状态正常返回true
     */
    bool check_robot_state();

    /**
     * @brief 子类特定的初始化步骤（子类可重写）
     * @return 初始化成功返回true
     */
    virtual bool on_init_specific() { return true; }

    /**
     * @brief 设置默认的运动参数
     */
    void set_default_movement_parameters();

protected:
    std::string robot_name_;        ///< 机器人名称
    std::string host_;              ///< 机器人IP地址
    int port_;                      ///< 机器人端口
    bool is_connected_;             ///< 连接状态
    bool is_initialized_;           ///< 初始化状态

    // 机器人服务接口
    ServiceInterface robot_service_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_ROBOT_BASE_H
