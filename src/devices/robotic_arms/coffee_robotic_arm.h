/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_ROBOTIC_ARM_H
#define AUBO_COFFEE_SERVICE_COFFEE_ROBOTIC_ARM_H

#include <memory>
#include <string>

#include <nlohmann/json.hpp>


namespace aubo {

/**
 * @class CoffeeRoboticArm
 * @brief 咖啡机械臂控制类
 *
 * 负责控制咖啡制作过程中的咖啡机械臂，主要功能包括拿杯子、接咖啡、交付咖啡等操作。
 */
class CoffeeRoboticArm {
public:
    /**
     * @brief 构造函数
     */
    CoffeeRoboticArm();

    /**
     * @brief 析构函数
     */
    ~CoffeeRoboticArm();

    /**
     * @brief 初始化机械臂
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机械臂连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    bool execute_action(const std::string& action_name, nlohmann::json action_params = nlohmann::json());

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_ROBOTIC_ARM_H
