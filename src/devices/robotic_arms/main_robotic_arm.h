/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_MAIN_ROBOTIC_ARM_H
#define AUBO_COFFEE_SERVICE_MAIN_ROBOTIC_ARM_H

#include <memory>
#include <string>

#include <nlohmann/json.hpp>


namespace aubo {

/**
 * @class MainRoboticArm
 * @brief 主机械臂控制类
 *
 * 负责控制咖啡制作过程中拿杯子、接咖啡、交付咖啡等主要操作。
 */
class MainRoboticArm {
public:
    /**
     * @brief 构造函数
     */
    MainRoboticArm();

    /**
     * @brief 析构函数
     */
    ~MainRoboticArm();

    /**
     * @brief 初始化机械臂
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机械臂连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 执行动作
     * @param action_name 动作名称
     * @param action_params 动作参数
     * @return 执行成功返回true
     */
    bool execute_action(const std::string& action_name, const std::map<std::string, std::any> &action_params = {});

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_MAIN_ROBOTIC_ARM_H
