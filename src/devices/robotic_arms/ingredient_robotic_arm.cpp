/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "ingredient_robotic_arm.h"

#include <chrono>
#include <cmath>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "../../core/coffee_order.h"
#include "latte_art_config.h"
#include "robotic_arm_base.h"

namespace aubo {

// 配置常量
constexpr const char* INGREDIENT_ROBOTIC_ARM_HOST = "*************";
constexpr int INGREDIENT_ROBOTIC_ARM_PORT = 8899;

class IngredientRoboticArm::Impl : public RoboticArmBase {
public:
    Impl(IngredientRoboticArm* parent) : RoboticArmBase("IngredientRoboticArm", INGREDIENT_ROBOTIC_ARM_HOST, INGREDIENT_ROBOTIC_ARM_PORT), parent_(parent) {}

    ~Impl() {
        LOG_INFO("[IngredientRoboticArm] ~Impl");
    }

    bool on_init_specific() override {
        LOG_INFO("[IngredientRoboticArm] 执行配料机械臂特定初始化");

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[IngredientRoboticArm] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[IngredientRoboticArm] 拉花配置加载成功");
        }

        LOG_INFO("[IngredientRoboticArm] 配料机械臂特定初始化完成");
        return true;
    }

    bool execute_action(const std::string& action_name, nlohmann::json action_params) override {
        LOG_INFO("[IngredientRoboticArm] 执行动作: {}", action_name);
        // TODO: 实现通用动作执行逻辑
        return true;
    }

private:
    LatteArtConfig latte_art_config_;
};

IngredientRoboticArm::IngredientRoboticArm() {
    impl_ = std::make_unique<Impl>(this);
}

IngredientRoboticArm::~IngredientRoboticArm() = default;

bool IngredientRoboticArm::init() {
    return impl_->init();
}

bool IngredientRoboticArm::shutdown() {
    return impl_->shutdown();
}

bool IngredientRoboticArm::emergency_stop() {
    return impl_->emergency_stop();
}

bool IngredientRoboticArm::execute_action(const std::string& action_name, nlohmann::json action_params) {
    return impl_->execute_action(action_name, action_params);
}

} // namespace aubo
