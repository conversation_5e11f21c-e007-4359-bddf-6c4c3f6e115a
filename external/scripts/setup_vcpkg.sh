#!/bin/bash

set -e

# Logging function with different levels
log() {
    local level=$1
    shift
    echo "[${level}] $*" >&2
}

# Show usage information
show_usage() {
    echo "Usage: $0 [architecture]"
    echo "Supported architectures: arm64-android, arm64-linux, x64-linux, arm64-osx, x64-osx"
    exit 1
}

# Error handling function
handle_error() {
    log "ERROR" "$1"
    exit 1
}

# Warning handling function
handle_warning() {
    log "WARN" "$1"
}

# Validate input parameters
VCPKG_ARCH=$1
if [ -n "$VCPKG_ARCH" ]; then
    case "$VCPKG_ARCH" in
        arm64-android|arm64-linux|x64-linux|arm64-osx|x64-osx) ;;
        *) show_usage ;;
    esac
fi

# Array for statically linked packages
static_packages=()

# Array for dynamically linked packages
dynamic_packages=()

# Configure packages based on architecture
configure_packages() {
    static_packages=(
            "gtest" "taskflow" "paho-mqtt" "cpprestsdk" "boost-asio"
        )

    dynamic_packages=(

    )
}

# Determine script directory
SCRIPTS_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
VCPKG_ROOT="$SCRIPTS_DIR/../vcpkg"
export VCPKG_ROOT

# Configure system settings
configure_system() {
    if [[ "$(uname -m)" =~ ^(arm64|aarch64)$ ]]; then
        export VCPKG_FORCE_SYSTEM_BINARIES=1
    fi
}

# Detect and configure environment
detect_and_configure_environment() {
    local OS_TYPE=$(uname)
    local ARCH_TYPE=$(uname -m)

    case "$OS_TYPE" in
        "Darwin")
            case "$ARCH_TYPE" in
                "arm64") VCPKG_ARCH="arm64-osx";;
                "x86_64") VCPKG_ARCH="x64-osx";;
                *) handle_error "Unsupported Darwin architecture: $ARCH_TYPE";;
            esac
            ;;
        "Linux")
            case "$ARCH_TYPE" in
                arm*) VCPKG_ARCH="arm-linux";;
                aarch64) VCPKG_ARCH="arm64-linux";;
                x86_64) VCPKG_ARCH="x64-linux";;
                *) handle_error "Unsupported Linux architecture: $ARCH_TYPE";;
            esac
            ;;
        *) handle_error "Unsupported operating system: $OS_TYPE";;
    esac
}

# Bootstrap vcpkg
bootstrap_vcpkg() {
    if [ ! -f "$VCPKG_ROOT/vcpkg" ]; then
        log "INFO" "Bootstrapping vcpkg..."
        (cd "$VCPKG_ROOT" && ./bootstrap-vcpkg.sh) || handle_error "Failed to bootstrap vcpkg"
        log "INFO" "Bootstrap completed."
    fi
}

# Configure triplets
configure_triplets() {
    local build_type_line="set(VCPKG_BUILD_TYPE release)"

    case "$VCPKG_ARCH" in
        "arm64-linux")
            cp triplets/community/arm64-linux.cmake triplets/arm64-linux.cmake
            cp triplets/community/x64-linux-dynamic.cmake triplets/arm64-linux-dynamic.cmake
            sed -i 's/x64/arm64/g' triplets/arm64-linux-dynamic.cmake
            ;;
        "x64-linux")
            cp triplets/community/x64-linux-dynamic.cmake triplets/x64-linux-dynamic.cmake
            ;;
        "arm64-android")
            cp triplets/arm64-android.cmake triplets/arm64-android-dynamic.cmake
            sed -i 's/set(VCPKG_LIBRARY_LINKAGE static)/set(VCPKG_LIBRARY_LINKAGE dynamic)/g' triplets/arm64-android-dynamic.cmake
            sed -i 's/set(VCPKG_CRT_LINKAGE static)/set(VCPKG_CRT_LINKAGE dynamic)/g' triplets/arm64-android-dynamic.cmake
            ;;
        *)
            cp triplets/community/$VCPKG_ARCH-release.cmake triplets/$VCPKG_ARCH.cmake
            cp triplets/community/$VCPKG_ARCH-dynamic.cmake triplets/$VCPKG_ARCH-dynamic.cmake
            ;;
    esac

    # Update triplet files
    for triplet_file in "$VCPKG_ROOT/triplets/$VCPKG_ARCH.cmake" "$VCPKG_ROOT/triplets/$VCPKG_ARCH-dynamic.cmake"; do
        if [ -f "$triplet_file" ] && ! grep -q "$build_type_line" "$triplet_file"; then
            echo "$build_type_line" >> "$triplet_file"
        fi
    done
}

# Fix dependencies
fix_dependencies() {
    if [ -d "$VCPKG_ROOT/ports/websocketpp" ]; then
        if [[ "$(uname)" == "Darwin" ]]; then
            sed -i '' 's/"boost-asio"/"asio"/g' "$VCPKG_ROOT/ports/websocketpp/vcpkg.json"
        else
            sed -i 's/"boost-asio"/"asio"/g' "$VCPKG_ROOT/ports/websocketpp/vcpkg.json"
        fi
    fi
}

# Install packages
install_packages() {
    local STATIC_VCPKG_ARCH="${VCPKG_ARCH}"
    local DYNAMIC_VCPKG_ARCH="${VCPKG_ARCH}-dynamic"

    log "INFO" "Preparing to install packages:"
    log "INFO" "Static libraries: ${static_packages[@]/%/:$STATIC_VCPKG_ARCH}"
    log "INFO" "Dynamic libraries: ${dynamic_packages[@]/%/:$DYNAMIC_VCPKG_ARCH}"

    $VCPKG_ROOT/vcpkg install \
        "${static_packages[@]/%/:$STATIC_VCPKG_ARCH}" \
        "${dynamic_packages[@]/%/:$DYNAMIC_VCPKG_ARCH}" \
        --cmake-args="-G Ninja" || handle_error "Package installation failed"
}

# Export installed packages
export_packages() {
    local EXPORT_DIR="$SCRIPTS_DIR/../../third_party"
    log "INFO" "Exporting packages to $EXPORT_DIR ..."

    # Create export directory
    mkdir -p "$EXPORT_DIR" || handle_error "Failed to create export directory"

    local OUTPUT_DIR_NAME="vcpkg"
    local OUTPUT_DIR="$EXPORT_DIR/$OUTPUT_DIR_NAME"

    # Clean export directory if it exists
    if [ -d "$OUTPUT_DIR" ]; then
        log "INFO" "Cleaning existing export directory..."
        rm -rf "$OUTPUT_DIR" || handle_error "Failed to clean export directory"
    fi

    # Process package names. "opencv4[core]" -> "opencv4"
    process_package_name() {
        local pkg=$1
        echo "${pkg%\[*}"
    }

    local packages_to_export=()
    for pkg in "${static_packages[@]}"; do
        local base_pkg=$(process_package_name "$pkg")
        packages_to_export+=("$base_pkg:$VCPKG_ARCH")
    done

    for pkg in "${dynamic_packages[@]}"; do
        local base_pkg=$(process_package_name "$pkg")
        packages_to_export+=("$base_pkg:$VCPKG_ARCH-dynamic")
    done

    log "INFO" "Preparing to export packages:"
    printf '%s\n' "${packages_to_export[@]}"

    $VCPKG_ROOT/vcpkg export "${packages_to_export[@]}" \
        --output-dir="$EXPORT_DIR" \
        --raw \
        --output="$OUTPUT_DIR_NAME" || handle_error "Package export failed"

    rm -rf "$OUTPUT_DIR/vcpkg"

    log "INFO" "Packages successfully exported to $EXPORT_DIR"
}

# Check dependencies
check_dependencies() {
    local deps=("git" "cmake" "ninja" "curl")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            handle_error "Missing required dependency: $dep"
        fi
    done
}

# Check disk space
check_disk_space() {
    local required_space=10 # GB
    local available_space=$(df -BG . | awk 'NR==2 {print $4}' | tr -d 'G')
    if [ "$available_space" -lt "$required_space" ]; then
        handle_error "Insufficient disk space. Required: ${required_space}GB"
    fi
}

# Clean previous build
clean_build() {
    log "INFO" "Cleaning previous build..."
    rm -rf "$VCPKG_ROOT/buildtrees"
    rm -rf "$VCPKG_ROOT/packages"
    rm -rf "$VCPKG_ROOT/downloads"
    rm -rf "$VCPKG_ROOT/installed"
}

# Main function
main() {
    check_dependencies
    check_disk_space

    [ -z "$VCPKG_ARCH" ] && detect_and_configure_environment
    configure_system
    configure_packages

    log "INFO" "Preparing vcpkg for $VCPKG_ARCH"
    cd "$VCPKG_ROOT" || handle_error "Cannot enter VCPKG_ROOT directory"

    bootstrap_vcpkg
    configure_triplets
    fix_dependencies
    clean_build

    log "INFO" "vcpkg setup completed. OS: $(uname), Architecture: $(uname -m)"
    log "INFO" "Installing vcpkg dependencies"

    install_packages
    log "INFO" "All vcpkg packages installed successfully!"

    export_packages
    log "INFO" "Installation and export process completed!"
}

# Execute main function
main "$@"
